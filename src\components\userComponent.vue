<template>
  <div>
    <q-btn-dropdown flat>
      <template v-slot:label>
        <div class="row items-center no-wrap">
          <q-avatar v-if="userImageUrl" class="q-mr-md">
            <img :src="userImageUrl" />
          </q-avatar>
          <q-avatar v-else icon="person" color="secondary" text-color="white" class="q-mr-md" />
          <div class="text-left user-font">
            <span class="text-bold">{{ currentUser?.name }}</span
            ><br />
            <span>{{ currentUser?.role }}</span>
          </div>
        </div>
      </template>

      <q-list>
        <q-item clickable v-close-popup>
          <q-item-section>
            <div class="btn-logout">
              <q-item-label class="text-white text-bold text-center" @click="handleLogout"
                >ออกจากระบบ</q-item-label
              >
            </div>
          </q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from 'src/stores/userStore'
import { useAuthStore } from 'src/stores/authStore'
import { useRouter } from 'vue-router'
import { ref, computed, watch } from 'vue'
import { UserService } from 'src/services/userService'

const userStore = useUserStore()
const authStore = useAuthStore()
const router = useRouter()
const userImageUrl = ref<string | null>(null)

// Get current user from auth store (which persists across page refreshes)
const currentUser = computed(() => {
  return authStore.currentUser || userStore.currentUser
})

// Watch for changes in current user to load image
watch(
  currentUser,
  async (newUser) => {
    if (newUser?.id && !userImageUrl.value) {
      try {
        const imageUrl = await UserService.getUserImageById(newUser.id)
        userImageUrl.value = imageUrl
      } catch (error) {
        console.error('Failed to load user image:', error)
      }
    }
  },
  { immediate: true },
)

// onMounted is no longer needed since watch handles image loading

const handleLogout = async () => {
  // Clear auth state and user data
  authStore.logout()
  userStore.clearCurrentUser()

  // Redirect to login page
  await router.push('/login')
  console.log('✅ User logged out successfully')
}
</script>

<style scoped>
.user-font {
  width: 80px;
  font-family: 'Noto Sans Thai', sans-serif;
  text-transform: none;
}

.btn-logout {
  background-color: #b53638;
  padding: 10px;
  border-radius: 8px;
}
</style>
