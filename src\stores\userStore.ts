import { defineStore } from 'pinia'
import { UserService } from 'src/services/userService'
import type { user } from 'src/types/user' // นำเข้า type user

export const useUserStore = defineStore('user', {
  state: () => ({
    users: [] as user[],
    currentUser: null as user | null,
    editingUser: null as user | null,
    loading: false,
    error: null as string | null,
  }),

  actions: {
    async fetchUsers() {
      this.loading = true
      this.error = null
      try {
        const users = await UserService.getAll()
        this.users = users
      } catch (err) {
        this.error = 'Failed to fetch users.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    async filterUsers(search: string = '', role: string = '') {
      this.loading = true
      this.error = null
      try {
        const users = await UserService.filterUsers(search, role)
        this.users = users
      } catch (err) {
        this.error = 'Failed to filter users.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    async login(name: string, password: string) {
      this.loading = true
      this.error = null
      try {
        // ใช้ UserService ในการ login
        const userInfo = await UserService.login(name, password)

        // เก็บข้อมูลผู้ใช้ที่ login สำเร็จ
        this.currentUser = userInfo
      } catch (err) {
        this.error = 'Invalid credentials.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    async fetchUserInfo(userId: number) {
      this.loading = true
      this.error = null
      try {
        const userInfo = await UserService.getUserInfo(userId)
        this.currentUser = userInfo
      } catch (err) {
        this.error = 'Failed to fetch user info.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    setCurrentUser(userInfo: user) {
      this.currentUser = userInfo
    },

    clearCurrentUser() {
      this.currentUser = null
    },

    setEditingUser(user: user) {
      this.editingUser = user
    },

    clearEditingUser() {
      this.editingUser = null
    },

    async addUser(userData: Partial<user>, imageFile?: File) {
      this.loading = true
      this.error = null
      try {
        // Generate new user ID based on the highest existing ID
        const maxId = this.users.length > 0 ? Math.max(...this.users.map((u) => u.id)) : 0
        const newUserId = maxId + 1

        // Prepare user data with generated ID and default values
        const newUserData = {
          id: newUserId,
          name: userData.name || '',
          password: userData.password || 'defaultPassword123', // You might want to generate a random password
          tel: userData.tel || '',
          role: userData.role || 'ประจำ',
          hour_work: userData.hour_work || 8,
          sick_level: userData.sick_level || 0,
          personal_leave: userData.personal_leave || 0,
          image: userData.image || 'noimage.png',
          branch: userData.branch || { id: 1, name: 'บางแสน', address: '' },
        }

        // Try to add user with image first, fallback to separate upload
        let createdUser
        try {
          // First attempt: Add user with image data included
          createdUser = await UserService.addUserWithImage(newUserData, imageFile)
        } catch (error) {
          console.warn('Failed to add user with image, trying without image:', error)

          // Fallback: Add user without image first
          createdUser = await UserService.addUser(newUserData)

          // Then try to upload image separately if provided
          if (imageFile && createdUser.id) {
            try {
              await UserService.uploadUserImage(createdUser.id, imageFile)
              console.log('Image uploaded successfully via separate endpoint')
            } catch (imageError) {
              console.warn('Image upload failed, but user was created successfully:', imageError)
              // Don't throw error - user creation should still succeed
            }
          }
        }

        // Add to local store
        this.users.push(createdUser)

        return createdUser
      } catch (err) {
        this.error = 'Failed to add user.'
        console.error(err)
        throw err
      } finally {
        this.loading = false
      }
    },

    async updateUser(userData: Partial<user>, imageFile?: File) {
      this.loading = true
      this.error = null
      try {
        // Update user data via API
        const updatedUser = await UserService.updateUser(userData)

        // Update image if provided
        if (imageFile && updatedUser.id) {
          try {
            await UserService.uploadUserImage(updatedUser.id, imageFile)
          } catch (imageError) {
            console.warn('Image upload failed, but user was updated successfully:', imageError)
          }
        }

        // Update in local store
        const index = this.users.findIndex((u) => u.id === updatedUser.id)
        if (index !== -1) {
          this.users[index] = updatedUser
        }

        return updatedUser
      } catch (err) {
        this.error = 'Failed to update user.'
        console.error(err)
        throw err
      } finally {
        this.loading = false
      }
    },

    async uploadUserImage(userId: number, imageFile: File) {
      try {
        const result = await UserService.uploadUserImage(userId, imageFile)
        return result
      } catch (err) {
        this.error = 'Failed to upload user image.'
        console.error(err)
        throw err
      }
    },

    async deleteUser(userId: number) {
      this.loading = true
      this.error = null
      try {
        // Call the delete API
        await UserService.deleteUser(userId)

        // Remove from local store
        this.users = this.users.filter((u) => u.id !== userId)

        return true
      } catch (err) {
        this.error = 'Failed to delete user.'
        console.error(err)
        throw err
      } finally {
        this.loading = false
      }
    },
  },

  getters: {
    allUsers: (state) => state.users,

    currentUserInfo: (state) => state.currentUser,
  },
})
